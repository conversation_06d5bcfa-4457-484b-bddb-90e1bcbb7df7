<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Video Player Library</title>
    <style>
        /* Import modern fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        h1 {
            text-align: center;
            color: white;
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        /* Modern Video Player CSS */
        modern-video {
            display: block;
            position: relative;
            width: 100%;
            max-width: 900px;
            margin: 2rem auto;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 20px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        modern-video:hover {
            transform: translateY(-8px);
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                0 0 40px rgba(102, 126, 234, 0.3);
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
            overflow: hidden;
        }

        .video-element {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 20px;
        }

        .video-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
            transition: all 0.4s ease;
            filter: brightness(0.8);
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(
                135deg,
                rgba(0, 0, 0, 0.3) 0%,
                rgba(0, 0, 0, 0.1) 50%,
                rgba(0, 0, 0, 0.3) 100%
            );
            z-index: 2;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(2px);
        }

        .play-button {
            width: 90px;
            height: 90px;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
        }

        .play-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .play-button:hover::before {
            left: 100%;
        }

        .play-button:hover {
            background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.9));
            transform: scale(1.15);
            box-shadow:
                0 12px 48px rgba(0, 0, 0, 0.4),
                0 0 0 4px rgba(102, 126, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .play-icon {
            width: 0;
            height: 0;
            border-left: 28px solid #333;
            border-top: 18px solid transparent;
            border-bottom: 18px solid transparent;
            margin-left: 6px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.2);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .video-hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            modern-video {
                border-radius: 16px;
                margin: 1.5rem auto;
            }

            .play-button {
                width: 70px;
                height: 70px;
            }

            .play-icon {
                border-left: 22px solid #333;
                border-top: 14px solid transparent;
                border-bottom: 14px solid transparent;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            h1 {
                font-size: 1.5rem;
            }

            modern-video {
                border-radius: 12px;
                margin: 1rem auto;
            }

            .play-button {
                width: 60px;
                height: 60px;
            }

            .play-icon {
                border-left: 18px solid #333;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
            }
        }
    </style>
</head>
<body>
    <!-- Demo Usage -->
    <h1>🎬 Modern Video Player</h1>

    <!-- Regular MP4 Video with working URL -->
    <modern-video
        src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        thumbnail="https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=800&h=450&fit=crop"
        width="800"
        height="450">
    </modern-video>

    <!-- YouTube Video -->
    <modern-video
        src="https://www.youtube.com/watch?v=jNQXAC9IVRw"
        width="800"
        height="450">
    </modern-video>

    <!-- Local video example -->
    <modern-video
        src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
        thumbnail="https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=800&h=450&fit=crop"
        data-autoplay="false"
        width="800"
        height="450">
    </modern-video>

    <script>
        class ModernVideo extends HTMLElement {
            constructor() {
                super();
                this.isLoaded = false;
                this.isPlaying = false;
                this.videoType = null;
                this.observer = null;
                this.videoElement = null;
            }

            connectedCallback() {
                this.render();
                this.setupLazyLoading();
                this.setupEventListeners();
            }

            disconnectedCallback() {
                if (this.observer) {
                    this.observer.disconnect();
                }
            }

            render() {
                const src = this.getAttribute('src');
                const thumbnail = this.getAttribute('thumbnail');
                const width = this.getAttribute('width') || '100%';
                const height = this.getAttribute('height') || 'auto';

                // Set dimensions properly
                if (width !== '100%') {
                    this.style.width = width.includes('px') ? width : width + 'px';
                }
                if (height !== 'auto') {
                    this.style.height = height.includes('px') ? height : height + 'px';
                }

                // Generate thumbnail from video type if not provided
                let thumbnailSrc = thumbnail;
                if (!thumbnailSrc && src) {
                    if (src.includes('youtube.com') || src.includes('youtu.be')) {
                        const videoId = this.extractYouTubeId(src);
                        thumbnailSrc = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
                    } else if (src.includes('vimeo.com')) {
                        // Vimeo thumbnail will be handled separately via API
                        thumbnailSrc = 'https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=800&h=450&fit=crop';
                    }
                }

                this.innerHTML = `
                    <div class="video-container">
                        ${thumbnailSrc ? `<img class="video-thumbnail" src="${thumbnailSrc}" alt="Video thumbnail" loading="lazy">` : ''}
                        <div class="video-overlay">
                            <div class="play-button">
                                <div class="play-icon"></div>
                            </div>
                        </div>
                    </div>
                `;
            }

            setupLazyLoading() {
                if ('IntersectionObserver' in window) {
                    this.observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting && !this.isLoaded) {
                                // Add small delay to ensure smooth loading
                                setTimeout(() => {
                                    if (entry.isIntersecting && !this.isLoaded) {
                                        this.loadVideo();
                                    }
                                }, 100);
                            }
                        });
                    }, {
                        rootMargin: '100px',
                        threshold: 0.1
                    });
                    this.observer.observe(this);
                } else {
                    // Fallback for older browsers - load immediately
                    setTimeout(() => this.loadVideo(), 500);
                }
            }

            setupEventListeners() {
                this.addEventListener('click', this.handleClick.bind(this));
            }

            handleClick(event) {
                event.preventDefault();
                if (!this.isLoaded) {
                    this.loadVideo();
                } else {
                    this.togglePlayPause();
                }
            }

            loadVideo() {
                if (this.isLoaded) return;

                const src = this.getAttribute('src');
                if (!src) {
                    console.error('ModernVideo: No src attribute provided');
                    return;
                }

                const autoplay = this.getAttribute('data-autoplay') === 'true';

                this.videoType = this.detectVideoType(src);
                this.showLoading();

                // Add error handling
                try {
                    setTimeout(() => {
                        this.createVideoElement(src, autoplay);
                        this.isLoaded = true;
                    }, 200);
                } catch (error) {
                    console.error('ModernVideo: Error loading video', error);
                    this.showError();
                }
            }

            detectVideoType(src) {
                if (src.includes('youtube.com') || src.includes('youtu.be')) {
                    return 'youtube';
                } else if (src.includes('vimeo.com')) {
                    return 'vimeo';
                } else {
                    return 'direct';
                }
            }

            showLoading() {
                const overlay = this.querySelector('.video-overlay');
                if (overlay) {
                    overlay.innerHTML = '<div class="loading-spinner"></div>';
                }
            }

            showError() {
                const overlay = this.querySelector('.video-overlay');
                if (overlay) {
                    overlay.innerHTML = `
                        <div style="color: white; text-align: center; font-family: Inter, sans-serif;">
                            <div style="font-size: 2rem; margin-bottom: 1rem;">⚠️</div>
                            <div>Video không thể tải</div>
                            <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.5rem;">Vui lòng thử lại sau</div>
                        </div>
                    `;
                }
            }

            createVideoElement(src, autoplay) {
                const container = this.querySelector('.video-container');
                const thumbnail = this.querySelector('.video-thumbnail');
                const overlay = this.querySelector('.video-overlay');

                if (!container) {
                    console.error('ModernVideo: Container not found');
                    return;
                }

                try {
                    switch (this.videoType) {
                        case 'youtube':
                            this.createYouTubePlayer(src, autoplay);
                            break;
                        case 'vimeo':
                            this.createVimeoPlayer(src, autoplay);
                            break;
                        default:
                            this.createDirectVideoPlayer(src, autoplay);
                            break;
                    }

                    // Smooth transition effects
                    if (thumbnail) {
                        thumbnail.style.transition = 'opacity 0.5s ease';
                        thumbnail.style.opacity = '0';
                        setTimeout(() => {
                            if (thumbnail.parentNode) {
                                thumbnail.remove();
                            }
                        }, 500);
                    }

                    if (overlay) {
                        overlay.style.transition = 'opacity 0.5s ease';
                        overlay.style.opacity = '0';
                        setTimeout(() => {
                            if (overlay.parentNode) {
                                overlay.remove();
                            }
                        }, 500);
                    }
                } catch (error) {
                    console.error('ModernVideo: Error creating video element', error);
                    this.showError();
                }
            }

            createYouTubePlayer(src, autoplay) {
                const videoId = this.extractYouTubeId(src);
                if (!videoId) {
                    console.error('ModernVideo: Invalid YouTube URL');
                    this.showError();
                    return;
                }

                const iframe = document.createElement('iframe');
                iframe.className = 'video-element';
                iframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&autoplay=${autoplay ? 1 : 0}&rel=0&modestbranding=1&playsinline=1`;
                iframe.frameBorder = '0';
                iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                iframe.allowFullscreen = true;
                iframe.loading = 'lazy';

                // Add error handling
                iframe.onerror = () => {
                    console.error('ModernVideo: YouTube iframe failed to load');
                    this.showError();
                };

                const container = this.querySelector('.video-container');
                if (container) {
                    container.appendChild(iframe);
                    this.videoElement = iframe;
                }
            }

            createVimeoPlayer(src, autoplay) {
                const videoId = this.extractVimeoId(src);
                if (!videoId) {
                    console.error('ModernVideo: Invalid Vimeo URL');
                    this.showError();
                    return;
                }

                const iframe = document.createElement('iframe');
                iframe.className = 'video-element';
                iframe.src = `https://player.vimeo.com/video/${videoId}?autoplay=${autoplay ? 1 : 0}&title=0&byline=0&portrait=0&playsinline=1`;
                iframe.frameBorder = '0';
                iframe.allow = 'autoplay; fullscreen; picture-in-picture';
                iframe.allowFullscreen = true;
                iframe.loading = 'lazy';

                // Add error handling
                iframe.onerror = () => {
                    console.error('ModernVideo: Vimeo iframe failed to load');
                    this.showError();
                };

                const container = this.querySelector('.video-container');
                if (container) {
                    container.appendChild(iframe);
                    this.videoElement = iframe;
                }
            }

            createDirectVideoPlayer(src, autoplay) {
                const video = document.createElement('video');
                video.className = 'video-element';
                video.src = src;
                video.controls = true;
                video.preload = 'metadata';
                video.crossOrigin = 'anonymous';

                if (autoplay) {
                    video.autoplay = true;
                    video.muted = true; // Required for autoplay in most browsers
                }

                // Optimize for performance
                video.setAttribute('playsinline', '');
                video.setAttribute('webkit-playsinline', '');

                // Add error handling
                video.onerror = () => {
                    console.error('ModernVideo: Video failed to load');
                    this.showError();
                };

                video.onloadstart = () => {
                    console.log('ModernVideo: Video loading started');
                };

                video.oncanplay = () => {
                    console.log('ModernVideo: Video can start playing');
                };

                const container = this.querySelector('.video-container');
                if (container) {
                    container.appendChild(video);
                    this.videoElement = video;
                }
            }

            togglePlayPause() {
                if (this.videoType === 'direct' && this.videoElement) {
                    try {
                        if (this.videoElement.paused) {
                            this.videoElement.play().catch(error => {
                                console.error('ModernVideo: Play failed', error);
                            });
                        } else {
                            this.videoElement.pause();
                        }
                    } catch (error) {
                        console.error('ModernVideo: Toggle play/pause failed', error);
                    }
                }
            }

            extractYouTubeId(url) {
                if (!url) return null;
                const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
                const match = url.match(regExp);
                return (match && match[2].length === 11) ? match[2] : null;
            }

            extractVimeoId(url) {
                if (!url) return null;
                const regExp = /vimeo.com\/(\d+)/;
                const match = url.match(regExp);
                return match ? match[1] : null;
            }
        }

        // Register the custom element
        customElements.define('modern-video', ModernVideo);

        // Add some demo styling
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ModernVideo library loaded successfully!');
        });
    </script>
</body>
</html>
        }

        // Register the custom element
        customElements.define('modern-video', ModernVideo);
    </script>
</body>
</html>
