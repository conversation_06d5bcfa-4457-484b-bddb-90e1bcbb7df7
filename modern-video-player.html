<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Video Player Library</title>
    <link rel="stylesheet" href="modern-video.css" />
    <script src="modern-video.js"></script>
</head>
<body>
    <!-- Demo Usage -->
    <h1>🎬 Modern Video Player</h1>

    <!-- Regular MP4 Video with working URL -->
    <modern-video
        src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        thumbnail="https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=800&h=450&fit=crop"
        width="800"
        height="450">
    </modern-video>

    <!-- YouTube Video -->
    <modern-video
        src="https://www.youtube.com/watch?v=840Vw6IB5Zw"
        width="800"
        height="800"
        data-autoplay="true"    
    >
    </modern-video>

    <!-- Local video example -->
    <modern-video
        src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
        thumbnail="https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=800&h=450&fit=crop"
        data-autoplay="false"
        width="800"
        height="450">
    </modern-video>

    <script>
        

        // Add some demo styling
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ModernVideo library loaded successfully!');
        });
    </script>
</body>
</html>
