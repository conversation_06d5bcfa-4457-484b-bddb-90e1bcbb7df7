<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Video Player Library</title>
    <style>
        /* Modern Video Player CSS */
        modern-video {
            display: block;
            position: relative;
            width: 100%;
            max-width: 100%;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        modern-video:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: #000;
        }

        .video-element {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
            transition: opacity 0.3s ease;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.3);
            z-index: 2;
            transition: all 0.3s ease;
        }

        .play-button {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .play-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        .play-icon {
            width: 0;
            height: 0;
            border-left: 25px solid #333;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            margin-left: 5px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .video-hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .play-button {
                width: 60px;
                height: 60px;
            }
            
            .play-icon {
                border-left: 18px solid #333;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
            }
        }

        @media (max-width: 480px) {
            .play-button {
                width: 50px;
                height: 50px;
            }
            
            .play-icon {
                border-left: 15px solid #333;
                border-top: 10px solid transparent;
                border-bottom: 10px solid transparent;
            }
        }
    </style>
</head>
<body>
    <!-- Demo Usage -->
    <h1>Modern Video Player Library Demo</h1>
    
    <!-- Regular MP4 Video -->
    <modern-video 
        src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
        thumbnail="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg"
        width="800"
        height="450">
    </modern-video>

    <br><br>

    <!-- YouTube Video -->
    <modern-video 
        src="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        data-autoplay="true"
        width="800"
        height="450">
    </modern-video>

    <br><br>

    <!-- Vimeo Video -->
    <modern-video 
        src="https://vimeo.com/148751763"
        width="800"
        height="450">
    </modern-video>

    <script>
        class ModernVideo extends HTMLElement {
            constructor() {
                super();
                this.isLoaded = false;
                this.isPlaying = false;
                this.videoType = null;
                this.observer = null;
                this.videoElement = null;
            }

            connectedCallback() {
                this.render();
                this.setupLazyLoading();
                this.setupEventListeners();
            }

            disconnectedCallback() {
                if (this.observer) {
                    this.observer.disconnect();
                }
            }

            render() {
                const src = this.getAttribute('src');
                const thumbnail = this.getAttribute('thumbnail');
                const width = this.getAttribute('width') || '100%';
                const height = this.getAttribute('height') || 'auto';

                this.style.width = width + (typeof width === 'number' ? 'px' : '');
                this.style.height = height + (typeof height === 'number' ? 'px' : '');

                this.innerHTML = `
                    <div class="video-container">
                        ${thumbnail ? `<img class="video-thumbnail" src="${thumbnail}" alt="Video thumbnail">` : ''}
                        <div class="video-overlay">
                            <div class="play-button">
                                <div class="play-icon"></div>
                            </div>
                        </div>
                    </div>
                `;
            }

            setupLazyLoading() {
                if ('IntersectionObserver' in window) {
                    this.observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting && !this.isLoaded) {
                                this.loadVideo();
                            }
                        });
                    }, {
                        rootMargin: '50px'
                    });
                    this.observer.observe(this);
                } else {
                    // Fallback for older browsers
                    this.loadVideo();
                }
            }

            setupEventListeners() {
                this.addEventListener('click', this.handleClick.bind(this));
            }

            handleClick(event) {
                event.preventDefault();
                if (!this.isLoaded) {
                    this.loadVideo();
                } else {
                    this.togglePlayPause();
                }
            }

            loadVideo() {
                if (this.isLoaded) return;

                const src = this.getAttribute('src');
                const autoplay = this.getAttribute('data-autoplay') === 'true';
                
                this.videoType = this.detectVideoType(src);
                this.showLoading();

                setTimeout(() => {
                    this.createVideoElement(src, autoplay);
                    this.isLoaded = true;
                }, 100);
            }

            detectVideoType(src) {
                if (src.includes('youtube.com') || src.includes('youtu.be')) {
                    return 'youtube';
                } else if (src.includes('vimeo.com')) {
                    return 'vimeo';
                } else {
                    return 'direct';
                }
            }

            showLoading() {
                const overlay = this.querySelector('.video-overlay');
                overlay.innerHTML = '<div class="loading-spinner"></div>';
            }

            createVideoElement(src, autoplay) {
                const container = this.querySelector('.video-container');
                const thumbnail = this.querySelector('.video-thumbnail');
                const overlay = this.querySelector('.video-overlay');

                switch (this.videoType) {
                    case 'youtube':
                        this.createYouTubePlayer(src, autoplay);
                        break;
                    case 'vimeo':
                        this.createVimeoPlayer(src, autoplay);
                        break;
                    default:
                        this.createDirectVideoPlayer(src, autoplay);
                        break;
                }

                if (thumbnail) {
                    thumbnail.style.opacity = '0';
                    setTimeout(() => thumbnail.remove(), 300);
                }
                
                overlay.style.opacity = '0';
                setTimeout(() => overlay.remove(), 300);
            }

            createYouTubePlayer(src, autoplay) {
                const videoId = this.extractYouTubeId(src);
                const iframe = document.createElement('iframe');
                iframe.className = 'video-element';
                iframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&autoplay=${autoplay ? 1 : 0}&rel=0`;
                iframe.frameBorder = '0';
                iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                iframe.allowFullscreen = true;
                
                this.querySelector('.video-container').appendChild(iframe);
                this.videoElement = iframe;
            }

            createVimeoPlayer(src, autoplay) {
                const videoId = this.extractVimeoId(src);
                const iframe = document.createElement('iframe');
                iframe.className = 'video-element';
                iframe.src = `https://player.vimeo.com/video/${videoId}?autoplay=${autoplay ? 1 : 0}&title=0&byline=0&portrait=0`;
                iframe.frameBorder = '0';
                iframe.allow = 'autoplay; fullscreen; picture-in-picture';
                iframe.allowFullscreen = true;
                
                this.querySelector('.video-container').appendChild(iframe);
                this.videoElement = iframe;
            }

            createDirectVideoPlayer(src, autoplay) {
                const video = document.createElement('video');
                video.className = 'video-element';
                video.src = src;
                video.controls = true;
                video.preload = 'metadata';
                if (autoplay) {
                    video.autoplay = true;
                    video.muted = true; // Required for autoplay in most browsers
                }
                
                // Optimize for performance
                video.setAttribute('playsinline', '');
                video.setAttribute('webkit-playsinline', '');
                
                this.querySelector('.video-container').appendChild(video);
                this.videoElement = video;
            }

            togglePlayPause() {
                if (this.videoType === 'direct' && this.videoElement) {
                    if (this.videoElement.paused) {
                        this.videoElement.play();
                    } else {
                        this.videoElement.pause();
                    }
                }
            }

            extractYouTubeId(url) {
                const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
                const match = url.match(regExp);
                return (match && match[2].length === 11) ? match[2] : null;
            }

            extractVimeoId(url) {
                const regExp = /vimeo.com\/(\d+)/;
                const match = url.match(regExp);
                return match ? match[1] : null;
            }
        }

        // Register the custom element
        customElements.define('modern-video', ModernVideo);
    </script>
</body>
</html>
