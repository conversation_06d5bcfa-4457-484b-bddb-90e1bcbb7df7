<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Video Player Library</title>
    <style>
        /* Import modern fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 70%);
            pointer-events: none;
            z-index: -1;
        }

        h1 {
            text-align: center;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            font-size: 3rem;
            margin-bottom: 4rem;
            animation: gradientShift 6s ease-in-out infinite;
            letter-spacing: -0.02em;
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: translateX(-50%) scaleX(1); }
            50% { opacity: 1; transform: translateX(-50%) scaleX(1.2); }
        }

        /* Modern Video Player CSS */
        modern-video {
            display: block;
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: 3rem auto;
            background:
                linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)),
                linear-gradient(145deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1));
            border-radius: 24px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        modern-video:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow:
                0 40px 80px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                0 0 60px rgba(120, 119, 198, 0.4),
                0 0 100px rgba(255, 119, 198, 0.2);
        }

        modern-video::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 50%,
                rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.6s ease;
            pointer-events: none;
            z-index: 1;
        }

        modern-video:hover::before {
            opacity: 1;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background:
                radial-gradient(circle at 30% 70%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0a, #1a1a1a);
            overflow: hidden;
            border-radius: 24px;
        }

        .video-element {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 24px;
            z-index: 2;
        }

        /* Custom YouTube Player Styling */
        .video-element.youtube-player {
            border: none !important;
            outline: none !important;
            background: transparent !important;
            filter: contrast(1.1) saturate(1.2) brightness(1.05);
        }

        /* Hide YouTube branding and controls overlay */
        .youtube-overlay-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 3;
            background:
                linear-gradient(to bottom,
                    transparent 0%,
                    transparent 85%,
                    rgba(0, 0, 0, 0.8) 95%,
                    rgba(0, 0, 0, 0.9) 100%),
                linear-gradient(to right,
                    transparent 0%,
                    transparent 85%,
                    rgba(0, 0, 0, 0.8) 95%,
                    rgba(0, 0, 0, 0.9) 100%);
            border-radius: 24px;
        }

        /* Enhanced glass morphism effect */
        .video-glass-effect {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 255, 255, 0.05) 50%,
                    rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(1px) saturate(1.1);
            -webkit-backdrop-filter: blur(1px) saturate(1.1);
            pointer-events: none;
            z-index: 4;
            border-radius: 24px;
            opacity: 0.7;
        }

        .video-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
            transition: all 0.4s ease;
            filter: brightness(0.8);
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background:
                radial-gradient(circle at center, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.6) 100%),
                linear-gradient(135deg,
                    rgba(120, 119, 198, 0.1) 0%,
                    rgba(255, 119, 198, 0.1) 50%,
                    rgba(120, 219, 255, 0.1) 100%
                );
            z-index: 10;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px) saturate(1.2);
            -webkit-backdrop-filter: blur(10px) saturate(1.2);
            border-radius: 24px;
        }

        .video-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                conic-gradient(from 0deg at 50% 50%,
                    rgba(120, 119, 198, 0.1) 0deg,
                    rgba(255, 119, 198, 0.1) 120deg,
                    rgba(120, 219, 255, 0.1) 240deg,
                    rgba(120, 119, 198, 0.1) 360deg);
            animation: rotate 20s linear infinite;
            opacity: 0.5;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .play-button {
            width: 100px;
            height: 100px;
            background:
                linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8)),
                radial-gradient(circle at 30% 30%, rgba(120, 119, 198, 0.2), transparent 70%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            box-shadow:
                0 15px 40px rgba(0, 0, 0, 0.4),
                0 5px 15px rgba(0, 0, 0, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            z-index: 11;
        }

        .play-button::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                conic-gradient(from 0deg,
                    transparent 0deg,
                    rgba(120, 119, 198, 0.3) 90deg,
                    rgba(255, 119, 198, 0.3) 180deg,
                    rgba(120, 219, 255, 0.3) 270deg,
                    transparent 360deg);
            animation: spinGlow 3s linear infinite;
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        .play-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.6),
                transparent);
            transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .play-button:hover::before {
            opacity: 1;
        }

        .play-button:hover::after {
            left: 100%;
        }

        .play-button:hover {
            background:
                linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.9)),
                radial-gradient(circle at 30% 30%, rgba(120, 119, 198, 0.3), transparent 70%);
            transform: scale(1.2) rotate(5deg);
            box-shadow:
                0 25px 60px rgba(0, 0, 0, 0.5),
                0 10px 25px rgba(0, 0, 0, 0.3),
                0 0 0 6px rgba(120, 119, 198, 0.2),
                0 0 0 12px rgba(255, 119, 198, 0.1),
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(0, 0, 0, 0.1);
        }

        @keyframes spinGlow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .play-icon {
            width: 0;
            height: 0;
            border-left: 32px solid #1a1a2e;
            border-top: 20px solid transparent;
            border-bottom: 20px solid transparent;
            margin-left: 8px;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
            transition: all 0.3s ease;
            z-index: 12;
            position: relative;
        }

        .play-button:hover .play-icon {
            border-left-color: #0f0f23;
            transform: scale(1.1);
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 5px solid rgba(255, 255, 255, 0.1);
            border-top: 5px solid #ff6b6b;
            border-right: 5px solid #4ecdc4;
            border-bottom: 5px solid #45b7d1;
            border-left: 5px solid #96ceb4;
            border-radius: 50%;
            animation: spinRainbow 1.5s linear infinite;
            box-shadow:
                0 0 30px rgba(255, 107, 107, 0.3),
                0 0 60px rgba(78, 205, 196, 0.2);
            position: relative;
        }

        .loading-spinner::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid transparent;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: spinRainbow 2s linear infinite reverse;
        }

        @keyframes spinRainbow {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .video-hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            modern-video {
                border-radius: 16px;
                margin: 1.5rem auto;
            }

            .play-button {
                width: 70px;
                height: 70px;
            }

            .play-icon {
                border-left: 22px solid #333;
                border-top: 14px solid transparent;
                border-bottom: 14px solid transparent;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            h1 {
                font-size: 1.5rem;
            }

            modern-video {
                border-radius: 12px;
                margin: 1rem auto;
            }

            .play-button {
                width: 60px;
                height: 60px;
            }

            .play-icon {
                border-left: 18px solid #333;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
            }
        }
    </style>
</head>
<body>
    <!-- Demo Usage -->
    <h1>🎬 Modern Video Player</h1>

    <!-- Regular MP4 Video with working URL -->
    <modern-video
        src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        thumbnail="https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=800&h=450&fit=crop"
        width="800"
        height="450">
    </modern-video>

    <!-- YouTube Video -->
    <modern-video
        src="https://www.youtube.com/watch?v=jNQXAC9IVRw"
        width="800"
        height="450">
    </modern-video>

    <!-- Local video example -->
    <modern-video
        src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
        thumbnail="https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=800&h=450&fit=crop"
        data-autoplay="false"
        width="800"
        height="450">
    </modern-video>

    <script>
        class ModernVideo extends HTMLElement {
            constructor() {
                super();
                this.isLoaded = false;
                this.isPlaying = false;
                this.videoType = null;
                this.observer = null;
                this.videoElement = null;
            }

            connectedCallback() {
                this.render();
                this.setupLazyLoading();
                this.setupEventListeners();
            }

            disconnectedCallback() {
                if (this.observer) {
                    this.observer.disconnect();
                }
            }

            render() {
                const src = this.getAttribute('src');
                const thumbnail = this.getAttribute('thumbnail');
                const width = this.getAttribute('width') || '100%';
                const height = this.getAttribute('height') || 'auto';

                // Set dimensions properly
                if (width !== '100%') {
                    this.style.width = width.includes('px') ? width : width + 'px';
                }
                if (height !== 'auto') {
                    this.style.height = height.includes('px') ? height : height + 'px';
                }

                // Generate thumbnail from video type if not provided
                let thumbnailSrc = thumbnail;
                if (!thumbnailSrc && src) {
                    if (src.includes('youtube.com') || src.includes('youtu.be')) {
                        const videoId = this.extractYouTubeId(src);
                        thumbnailSrc = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
                    } else if (src.includes('vimeo.com')) {
                        // Vimeo thumbnail will be handled separately via API
                        thumbnailSrc = 'https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=800&h=450&fit=crop';
                    }
                }

                this.innerHTML = `
                    <div class="video-container">
                        ${thumbnailSrc ? `<img class="video-thumbnail" src="${thumbnailSrc}" alt="Video thumbnail" loading="lazy">` : ''}
                        <div class="video-overlay">
                            <div class="play-button">
                                <div class="play-icon"></div>
                            </div>
                        </div>
                    </div>
                `;
            }

            setupLazyLoading() {
                if ('IntersectionObserver' in window) {
                    this.observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting && !this.isLoaded) {
                                // Add small delay to ensure smooth loading
                                setTimeout(() => {
                                    if (entry.isIntersecting && !this.isLoaded) {
                                        this.loadVideo();
                                    }
                                }, 100);
                            }
                        });
                    }, {
                        rootMargin: '100px',
                        threshold: 0.1
                    });
                    this.observer.observe(this);
                } else {
                    // Fallback for older browsers - load immediately
                    setTimeout(() => this.loadVideo(), 500);
                }
            }

            setupEventListeners() {
                this.addEventListener('click', this.handleClick.bind(this));
            }

            handleClick(event) {
                event.preventDefault();
                if (!this.isLoaded) {
                    this.loadVideo();
                } else {
                    this.togglePlayPause();
                }
            }

            loadVideo() {
                if (this.isLoaded) return;

                const src = this.getAttribute('src');
                if (!src) {
                    console.error('ModernVideo: No src attribute provided');
                    return;
                }

                const autoplay = this.getAttribute('data-autoplay') === 'true';

                this.videoType = this.detectVideoType(src);
                this.showLoading();

                // Add error handling
                try {
                    setTimeout(() => {
                        this.createVideoElement(src, autoplay);
                        this.isLoaded = true;
                    }, 200);
                } catch (error) {
                    console.error('ModernVideo: Error loading video', error);
                    this.showError();
                }
            }

            detectVideoType(src) {
                if (src.includes('youtube.com') || src.includes('youtu.be')) {
                    return 'youtube';
                } else if (src.includes('vimeo.com')) {
                    return 'vimeo';
                } else {
                    return 'direct';
                }
            }

            showLoading() {
                const overlay = this.querySelector('.video-overlay');
                if (overlay) {
                    overlay.innerHTML = '<div class="loading-spinner"></div>';
                }
            }

            showError() {
                const overlay = this.querySelector('.video-overlay');
                if (overlay) {
                    overlay.innerHTML = `
                        <div style="color: white; text-align: center; font-family: Inter, sans-serif;">
                            <div style="font-size: 2rem; margin-bottom: 1rem;">⚠️</div>
                            <div>Video không thể tải</div>
                            <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.5rem;">Vui lòng thử lại sau</div>
                        </div>
                    `;
                }
            }

            createVideoElement(src, autoplay) {
                const container = this.querySelector('.video-container');
                const thumbnail = this.querySelector('.video-thumbnail');
                const overlay = this.querySelector('.video-overlay');

                if (!container) {
                    console.error('ModernVideo: Container not found');
                    return;
                }

                try {
                    switch (this.videoType) {
                        case 'youtube':
                            this.createYouTubePlayer(src, autoplay);
                            break;
                        case 'vimeo':
                            this.createVimeoPlayer(src, autoplay);
                            break;
                        default:
                            this.createDirectVideoPlayer(src, autoplay);
                            break;
                    }

                    // Smooth transition effects
                    if (thumbnail) {
                        thumbnail.style.transition = 'opacity 0.5s ease';
                        thumbnail.style.opacity = '0';
                        setTimeout(() => {
                            if (thumbnail.parentNode) {
                                thumbnail.remove();
                            }
                        }, 500);
                    }

                    if (overlay) {
                        overlay.style.transition = 'opacity 0.5s ease';
                        overlay.style.opacity = '0';
                        setTimeout(() => {
                            if (overlay.parentNode) {
                                overlay.remove();
                            }
                        }, 500);
                    }
                } catch (error) {
                    console.error('ModernVideo: Error creating video element', error);
                    this.showError();
                }
            }

            createYouTubePlayer(src, autoplay) {
                const videoId = this.extractYouTubeId(src);
                if (!videoId) {
                    console.error('ModernVideo: Invalid YouTube URL');
                    this.showError();
                    return;
                }

                const iframe = document.createElement('iframe');
                iframe.className = 'video-element youtube-player';

                // Enhanced YouTube embed with custom styling parameters
                iframe.src = `https://www.youtube.com/embed/${videoId}?` +
                    `enablejsapi=1&` +
                    `autoplay=${autoplay ? 1 : 0}&` +
                    `rel=0&` +
                    `modestbranding=1&` +
                    `playsinline=1&` +
                    `controls=1&` +
                    `showinfo=0&` +
                    `fs=1&` +
                    `cc_load_policy=0&` +
                    `iv_load_policy=3&` +
                    `autohide=1&` +
                    `color=white&` +
                    `theme=dark`;

                iframe.frameBorder = '0';
                iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                iframe.allowFullscreen = true;
                iframe.loading = 'lazy';

                // Remove default YouTube styling
                iframe.style.border = 'none';
                iframe.style.outline = 'none';
                iframe.style.background = 'transparent';

                // Add error handling
                iframe.onerror = () => {
                    console.error('ModernVideo: YouTube iframe failed to load');
                    this.showError();
                };

                const container = this.querySelector('.video-container');
                if (container) {
                    container.appendChild(iframe);
                    this.videoElement = iframe;

                    // Add custom overlay for YouTube branding removal
                    this.addYouTubeCustomStyling();
                }
            }

            addYouTubeCustomStyling() {
                const container = this.querySelector('.video-container');
                if (!container) return;

                // Create YouTube branding mask
                const brandingMask = document.createElement('div');
                brandingMask.className = 'youtube-overlay-mask';
                container.appendChild(brandingMask);

                // Create glass morphism effect
                const glassEffect = document.createElement('div');
                glassEffect.className = 'video-glass-effect';
                container.appendChild(glassEffect);

                // Add custom CSS to hide YouTube elements via injection
                const style = document.createElement('style');
                style.textContent = `
                    /* Hide YouTube logo and branding */
                    .ytp-chrome-top-buttons,
                    .ytp-youtube-button,
                    .ytp-watermark,
                    .ytp-chrome-top,
                    .ytp-show-cards-title,
                    .ytp-cards-teaser,
                    .ytp-endscreen-element,
                    .ytp-ce-element,
                    .ytp-suggested-action {
                        display: none !important;
                        opacity: 0 !important;
                        visibility: hidden !important;
                    }

                    /* Custom YouTube player styling */
                    .html5-video-player {
                        border-radius: 24px !important;
                        overflow: hidden !important;
                    }

                    /* Hide annotations and cards */
                    .annotation,
                    .ytp-cards-button,
                    .ytp-cards-teaser {
                        display: none !important;
                    }
                `;

                // Inject the style into the document head
                if (!document.querySelector('#youtube-custom-style')) {
                    style.id = 'youtube-custom-style';
                    document.head.appendChild(style);
                }
            }

            createVimeoPlayer(src, autoplay) {
                const videoId = this.extractVimeoId(src);
                if (!videoId) {
                    console.error('ModernVideo: Invalid Vimeo URL');
                    this.showError();
                    return;
                }

                const iframe = document.createElement('iframe');
                iframe.className = 'video-element';
                iframe.src = `https://player.vimeo.com/video/${videoId}?autoplay=${autoplay ? 1 : 0}&title=0&byline=0&portrait=0&playsinline=1`;
                iframe.frameBorder = '0';
                iframe.allow = 'autoplay; fullscreen; picture-in-picture';
                iframe.allowFullscreen = true;
                iframe.loading = 'lazy';

                // Add error handling
                iframe.onerror = () => {
                    console.error('ModernVideo: Vimeo iframe failed to load');
                    this.showError();
                };

                const container = this.querySelector('.video-container');
                if (container) {
                    container.appendChild(iframe);
                    this.videoElement = iframe;
                }
            }

            createDirectVideoPlayer(src, autoplay) {
                const video = document.createElement('video');
                video.className = 'video-element';
                video.src = src;
                video.controls = true;
                video.preload = 'metadata';
                video.crossOrigin = 'anonymous';

                if (autoplay) {
                    video.autoplay = true;
                    video.muted = true; // Required for autoplay in most browsers
                }

                // Optimize for performance
                video.setAttribute('playsinline', '');
                video.setAttribute('webkit-playsinline', '');

                // Add error handling
                video.onerror = () => {
                    console.error('ModernVideo: Video failed to load');
                    this.showError();
                };

                video.onloadstart = () => {
                    console.log('ModernVideo: Video loading started');
                };

                video.oncanplay = () => {
                    console.log('ModernVideo: Video can start playing');
                };

                const container = this.querySelector('.video-container');
                if (container) {
                    container.appendChild(video);
                    this.videoElement = video;
                }
            }

            togglePlayPause() {
                if (this.videoType === 'direct' && this.videoElement) {
                    try {
                        if (this.videoElement.paused) {
                            this.videoElement.play().catch(error => {
                                console.error('ModernVideo: Play failed', error);
                            });
                        } else {
                            this.videoElement.pause();
                        }
                    } catch (error) {
                        console.error('ModernVideo: Toggle play/pause failed', error);
                    }
                }
            }

            extractYouTubeId(url) {
                if (!url) return null;
                const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
                const match = url.match(regExp);
                return (match && match[2].length === 11) ? match[2] : null;
            }

            extractVimeoId(url) {
                if (!url) return null;
                const regExp = /vimeo.com\/(\d+)/;
                const match = url.match(regExp);
                return match ? match[1] : null;
            }
        }

        // Register the custom element
        customElements.define('modern-video', ModernVideo);

        // Add some demo styling
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ModernVideo library loaded successfully!');
        });
    </script>
</body>
</html>
        }

        // Register the custom element
        customElements.define('modern-video', ModernVideo);
    </script>
</body>
</html>
