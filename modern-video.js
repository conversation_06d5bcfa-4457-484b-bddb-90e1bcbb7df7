class ModernVideo extends HTMLElement{constructor(){super(),this.isLoaded=!1,this.isPlaying=!1,this.videoType=null,this.observer=null,this.videoElement=null}connectedCallback(){this.render(),this.setupLazyLoading(),this.setupEventListeners()}disconnectedCallback(){this.observer&&this.observer.disconnect()}render(){const e=this.getAttribute("src"),t=this.getAttribute("thumbnail"),o=this.getAttribute("width"),i=this.getAttribute("height");o&&(this.style.width=o.includes("px")?o:o+"px",this.style.maxWidth="none"),i&&(this.style.height=i.includes("px")?i:i+"px",this.classList.add("has-explicit-dimensions"));let n=t;if(!n&&e)if(e.includes("youtube.com")||e.includes("youtu.be")){n=`https://img.youtube.com/vi/${this.extractYouTubeId(e)}/maxresdefault.jpg`}else e.includes("vimeo.com")&&(n="https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=800&h=450&fit=crop");this.innerHTML=`\n                    <div class="video-container">\n                        ${n?`<img class="video-thumbnail" src="${n}" alt="Video thumbnail" loading="lazy">`:""}\n                        <div class="video-overlay">\n                            <div class="play-button">\n                                <div class="play-icon"></div>\n                            </div>\n                        </div>\n                    </div>\n                `}setupLazyLoading(){if("IntersectionObserver"in window){let e;this.observer=new IntersectionObserver((t=>{e&&clearTimeout(e),e=setTimeout((()=>{t.forEach((e=>{e.isIntersecting&&!this.isLoaded&&this.loadVideo()}))}),100)}),{rootMargin:"100px",threshold:.1}),this.observer.observe(this)}else setTimeout((()=>this.loadVideo()),500)}setupEventListeners(){this.addEventListener("click",this.handleClick.bind(this))}handleClick(e){e.preventDefault(),this.isLoaded?this.togglePlayPause():this.loadVideo()}loadVideo(){if(this.isLoaded)return;const e=this.getAttribute("src");if(!e)return void console.error("ModernVideo: No src attribute provided");const t="true"===this.getAttribute("data-autoplay");this.videoType=this.detectVideoType(e),this.showLoading();try{requestAnimationFrame((()=>{this.createVideoElement(e,t),this.isLoaded=!0}))}catch(e){console.error("ModernVideo: Error loading video",e),this.showError()}}detectVideoType(e){return e.includes("youtube.com")||e.includes("youtu.be")?"youtube":e.includes("vimeo.com")?"vimeo":"direct"}showLoading(){const e=this.querySelector(".video-overlay");e&&(e.innerHTML='<div class="loading-spinner"></div>')}showError(){const e=this.querySelector(".video-overlay");e&&(e.innerHTML='\n                        <div style="color: white; text-align: center; font-family: Inter, sans-serif;">\n                            <div style="font-size: 2rem; margin-bottom: 1rem;">⚠️</div>\n                            <div>Video không thể tải</div>\n                            <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.5rem;">Vui lòng thử lại sau</div>\n                        </div>\n                    ')}createVideoElement(e,t){const o=this.querySelector(".video-container"),i=this.querySelector(".video-thumbnail"),n=this.querySelector(".video-overlay");if(o)try{switch(this.videoType){case"youtube":this.createYouTubePlayer(e,t);break;case"vimeo":this.createVimeoPlayer(e,t);break;default:this.createDirectVideoPlayer(e,t)}i&&(i.style.opacity="0",setTimeout((()=>{i.parentNode&&i.remove()}),300)),n&&(n.style.opacity="0",setTimeout((()=>{n.parentNode&&n.remove()}),300))}catch(e){console.error("ModernVideo: Error creating video element",e),this.showError()}else console.error("ModernVideo: Container not found")}createYouTubePlayer(e,t){const o=this.extractYouTubeId(e);if(!o)return console.error("ModernVideo: Invalid YouTube URL"),void this.showError();const i=document.createElement("iframe");i.className="video-element youtube-player",i.src=`https://www.youtube.com/embed/${o}?enablejsapi=1&autoplay=${t?1:0}&mute=${t?1:0}&rel=0&modestbranding=1&playsinline=1&controls=1&showinfo=0&fs=1&cc_load_policy=0&iv_load_policy=3&autohide=1&color=white&theme=dark&start=0`,i.frameBorder="0",i.allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",i.allowFullscreen=!0,i.loading="lazy",i.style.border="none",i.style.outline="none",i.style.background="transparent",i.onerror=()=>{console.error("ModernVideo: YouTube iframe failed to load"),this.showError()};const n=this.querySelector(".video-container");n&&(n.appendChild(i),this.videoElement=i,this.addYouTubeCustomStyling())}addYouTubeCustomStyling(){const e=this.querySelector(".video-container");if(!e)return;const t=document.createElement("div");t.className="youtube-overlay-mask",e.appendChild(t);const o=document.createElement("div");o.className="video-glass-effect",e.appendChild(o);const i=document.createElement("style");i.textContent="\n                    /* Hide YouTube logo and branding */\n                    .ytp-chrome-top-buttons,\n                    .ytp-youtube-button,\n                    .ytp-watermark,\n                    .ytp-chrome-top,\n                    .ytp-show-cards-title,\n                    .ytp-cards-teaser,\n                    .ytp-endscreen-element,\n                    .ytp-ce-element,\n                    .ytp-suggested-action {\n                        display: none !important;\n                        opacity: 0 !important;\n                        visibility: hidden !important;\n                    }\n\n                    /* Custom YouTube player styling */\n                    .html5-video-player {\n                        border-radius: 24px !important;\n                        overflow: hidden !important;\n                    }\n\n                    /* Hide annotations and cards */\n                    .annotation,\n                    .ytp-cards-button,\n                    .ytp-cards-teaser {\n                        display: none !important;\n                    }\n                ",document.querySelector("#youtube-custom-style")||(i.id="youtube-custom-style",document.head.appendChild(i))}createVimeoPlayer(e,t){const o=this.extractVimeoId(e);if(!o)return console.error("ModernVideo: Invalid Vimeo URL"),void this.showError();const i=document.createElement("iframe");i.className="video-element",i.src=`https://player.vimeo.com/video/${o}?autoplay=${t?1:0}&muted=${t?1:0}&title=0&byline=0&portrait=0&playsinline=1`,i.frameBorder="0",i.allow="autoplay; fullscreen; picture-in-picture",i.allowFullscreen=!0,i.loading="lazy",i.onerror=()=>{console.error("ModernVideo: Vimeo iframe failed to load"),this.showError()};const n=this.querySelector(".video-container");n&&(n.appendChild(i),this.videoElement=i)}createDirectVideoPlayer(e,t){const o=document.createElement("video");o.className="video-element",o.src=e,o.controls=!0,o.preload="metadata",o.crossOrigin="anonymous",t&&(o.autoplay=!0,o.muted=!0,o.setAttribute("autoplay",""),o.setAttribute("muted",""),o.addEventListener("loadeddata",(()=>{o.play().catch((e=>{console.log("ModernVideo: Autoplay prevented by browser policy")}))}))),o.setAttribute("playsinline",""),o.setAttribute("webkit-playsinline",""),o.style.transform="translateZ(0)",o.style.willChange="transform",o.style.backfaceVisibility="hidden",o.style.perspective="1000px",o.onerror=()=>{console.error("ModernVideo: Video failed to load"),this.showError()},o.onloadstart=()=>{console.log("ModernVideo: Video loading started")},o.oncanplay=()=>{console.log("ModernVideo: Video can start playing")};const i=this.querySelector(".video-container");i&&(i.appendChild(o),this.videoElement=o)}togglePlayPause(){if("direct"===this.videoType&&this.videoElement)try{this.videoElement.paused?this.videoElement.play().catch((e=>{console.error("ModernVideo: Play failed",e)})):this.videoElement.pause()}catch(e){console.error("ModernVideo: Toggle play/pause failed",e)}}extractYouTubeId(e){if(!e)return null;const t=e.match(/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/);return t&&11===t[2].length?t[2]:null}extractVimeoId(e){if(!e)return null;const t=e.match(/vimeo.com\/(\d+)/);return t?t[1]:null}}customElements.define("modern-video",ModernVideo);